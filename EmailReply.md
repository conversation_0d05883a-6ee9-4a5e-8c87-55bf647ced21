## 🔹 Reply Template

**As a Cinch support representative, I am drafting a response to a user email regarding:**
*********************************************************************
帮我回复邮件，它这个原因是由于外部声卡造成的给我说USB耳机这样的东西让他把映客输出无法自带的声卡这样就会解决问题
*********************************************************************
**Here’s a draft from a user:**
*********************************************************************
Short update;

 

Trackinfo is not stored in Spotify songs.
Identify Music info button retrieves nothing. I am now testing with music brainz picard to retrieve and write ID3 tags to the unidentified tract in Cinch.
Track counter is not set to zero if the map with tracks is emptied.
Outlook and Gmail detect the AudioNoiseDiagnostic tool as a virus (false positive?).
 

My provider blocked my account completely, due to my effort to send back the diagnotic files. The file created by AudioNoiseDiagnostic does not get past the virus scanner if sent back to you guys. In the mean time I had control of my account back 

 

I did my best to provide you with as much info about the Beta version, but (unfortunately) I see little progress. I tried to record songs from the webversion of Spotify and the installed app. Both with the same results.

 

I am no software developer, but maybe you need to write a script so cinch retrieves data from the source it records? For instance, if you want to record from Spotify, you need to select “the spotify button”, or “the soundcloud button”. In that way, maybe Cinch is better able to retrieve the Trakdata from the specific scource.

***********************************************************************

**Please improve this message to make it:**
- Clear and professional  
- Friendly but not overly casual  
- Helpful and action-oriented  
- Short enough for a quick email reply  

**Also, make sure it includes:**
- A thank-you, if appropriate  
- Any steps the user should take  
- Encouragement to follow up if the issue persists  

Important: Generate only the final message text, formatted and ready to send - no additional commentary or formatting instructions. No Best regards, sender's info etc. The output should be clean and directly copyable into my email response.
***********************************************************************


## 🔹 Improve reply Template
Please transform this draft into a polished email reply. Generate only the final message text, formatted and ready to send - no additional commentary or formatting instructions. The output should be clean and directly copyable into my email response.

