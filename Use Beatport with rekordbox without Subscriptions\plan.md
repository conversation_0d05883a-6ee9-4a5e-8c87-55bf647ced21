# 文章创作执行计划

## 用户需求和目标

### 文章主题
Use Beatport with rekordbox without Subscriptions

### 核心需求
- **文章长度**: 1600字（不能少，最多可超出20%）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据、趋势和案例研究
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: C - "What if" Scenario Opening
- **SEO关键词**: Use Beatport with rekordbox without Subscriptions

### 四大内容质量评估维度
1. **Effort (努力程度)**: 内容中必须体现明显的人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量，避免全网内容的"炒冷饭"
3. **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确，避免错误信息

### 信息增量要求
- 每篇文章必须包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案，而非泛泛而谈

### 推荐产品
**Cinch Audio Recorder** - 官方产品页面：https://www.cinchsolution.com/cinch-audio-recorder/

## 执行步骤详细清单

### 步骤1：基础"超级大纲"生成
- [ ] 从参考URL提取H2至H4级标题
- [ ] 合并整理提取的标题，标记源数量
- [ ] 按层级结构重新组织，形成初步框架大纲
- [ ] 保存至 `super_outline.md`

### 步骤2：创建最终文章大纲
- [ ] 基于超级大纲进行竞品内容空白分析
- [ ] 挖掘独特价值点（至少3个）
- [ ] 为每个主要章节准备人工经验要素
- [ ] 添加Introduction、Conclusion和FAQs
- [ ] 进行智能字数分配（目标1600字）
- [ ] 保存至 `final_outline.md`

### 步骤3：创作初稿
- [ ] 使用最终大纲撰写完整文章
- [ ] 确保每个章节达到目标字数
- [ ] 整合Cinch Audio Recorder产品推荐
- [ ] 保存至 `first_draft.md`

### 步骤4：生成SEO内容
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image图片提示词
- [ ] 保存至 `seo_metadata_images.md`

## 完成标准和检查点

### 大纲阶段检查点
- [ ] 是否包含至少3个竞品文章未涵盖的独特观点？
- [ ] 是否为每个H2章节准备了人工经验要素？
- [ ] 是否识别并准备解决用户的具体痛点？
- [ ] 是否包含可验证的准确信息和数据？
- [ ] 是否体现了作者的专业判断和建议？
- [ ] 所有章节字数分配总和是否在目标范围内？

### 初稿阶段检查点
- [ ] 文章总字数是否达到1600字要求？
- [ ] 是否正确整合了Cinch Audio Recorder产品推荐？
- [ ] 是否包含个人经验和试错故事？
- [ ] 是否提供了实用的解决方案？

## 预期输出文件清单

1. `plan.md` - 执行计划文件
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO内容和图片提示词

## 参考资源

### 参考URL
- https://www.viwizard.com/record-audio/beatport-to-rekordbox.html

### 产品指南
- `New_article/car_guide.md` - Cinch Audio Recorder详细信息

### 工作流程文件
- `New_article/outline.md` - 大纲生成工作流程
- `New_article/first_draft.md` - 初稿创作工作流程
- `New_article/seo_titles.md` - SEO内容生成工作流程
