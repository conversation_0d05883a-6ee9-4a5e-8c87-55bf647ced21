# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Download Tubidy MP3 Music
- **SEO关键词**: Download Tubidy MP3 Music
- **目标字数**: 1600字（最多可超出20%，即最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: D (Personal Experience/Case Study Opening)
- **推荐产品**: Cinch Audio Recorder

## 执行步骤详细清单

### 第1步：需求提取 ✅
- [x] 读取并分析 `info_aia.md` 中的所有要求
- [x] 理解四大内容质量评估维度（努力程度、原创性、专业能力、准确性）
- [x] 确认信息增量要求（3-5个独特观点）
- [x] 理解产品推荐指南和写作原则

### 第2步：大纲生成
- [ ] 提取参考URL的H2-H4标题
- [ ] 创建超级大纲（`super_outline.md`）
- [ ] 进行竞品内容空白分析
- [ ] 挖掘独特价值点
- [ ] 创建最终大纲（`final_outline.md`）
- [ ] 进行字数分配和验证

### 第3步：初稿创作
- [ ] 基于最终大纲撰写文章
- [ ] 确保字数符合要求（1600字）
- [ ] 保存为 `first_draft.md`

### 第4步：SEO内容生成
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存至 `seo_metadata_images.md`

## 完成标准和检查点

### 内容质量检查
- [ ] 包含至少3个竞品文章未涵盖的独特观点
- [ ] 每个H2章节包含人工经验要素
- [ ] 识别并解决用户具体痛点
- [ ] 包含可验证的准确信息和数据
- [ ] 体现作者专业判断和建议

### 字数控制检查
- [ ] 总字数在1600-1920字范围内
- [ ] 各章节字数分配合理
- [ ] 核心推荐产品章节占20-25%

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初始超级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO元数据和图片提示词

## 参考资源
- 用户需求文件: `New_article/info_aia.md`
- 产品指南: `New_article/car_guide.md`
- 参考URL1: https://www.viwizard.com/record-audio/download-tubidy-mp3-music.html
- 参考URL2: https://artdaily.com/news/171894/Step-by-Step-Tutorial-for-Downloading-Music-from-Tubidy
